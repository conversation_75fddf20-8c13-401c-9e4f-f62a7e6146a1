services:
  postgres:
    image: postgres:17-alpine
    container_name: shadcn-template-postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: shadcn_template
      # Required for the password salt in seed script
      PASSWORD_SALT: sit-environment-password-salt
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./server/prisma:/docker-entrypoint-initdb.d/prisma:ro
      - ./db-init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
      
  server:
    build:
      context: ./server
      dockerfile: Dockerfile
      target: development
    container_name: shadcn-template-server
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      NODE_ENV: development
      DATABASE_URL: ********************************************/shadcn_template?schema=public
      CLIENT_URL: http://client:3000
      ADMIN_URL: http://client-admin:3002
      SESSION_SECRET: sit-environment-session-secret
      PASSWORD_SALT: sit-environment-password-salt
      AUTH_SECRET: "sit-environment-auth-secret"
      # Add other environment variables as needed
    volumes:
      - ./server:/app
      - server-node-modules:/app/node_modules
    command: sh -c "yarn prisma:generate && yarn dev"
    depends_on:
      postgres:
        condition: service_healthy

  client:
    build:
      context: ./client
      dockerfile: Dockerfile
      target: development
    container_name: shadcn-template-client
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      NEXT_PUBLIC_API_URL: http://server:3001
      AUTH_TRUST_HOST: "true"
      AUTH_SECRET: "sit-environment-auth-secret"
      # Add other environment variables as needed
    volumes:
      - ./client:/app
      - client-node-modules:/app/node_modules
    depends_on:
      server:
        condition: service_started
    command: yarn dev

  client-admin:
    build:
      context: ./client-admin
      dockerfile: Dockerfile
      target: development
    container_name: shadcn-template-client-admin
    restart: unless-stopped
    ports:
      - "3002:3000"
    environment:
      NEXT_PUBLIC_API_URL: http://server:3001
      AUTH_TRUST_HOST: "true" 
      AUTH_SECRET: "sit-environment-auth-secret"
      # Add other environment variables as needed
    volumes:
      - ./client-admin:/app
      - client-admin-node-modules:/app/node_modules
    depends_on:
      server:
        condition: service_started
    command: yarn dev

volumes:
  postgres-data:
  server-node-modules:
  client-node-modules:
  client-admin-node-modules:
