# Development stage
FROM node:18-alpine AS development
WORKDIR /app

# Install yarn
RUN corepack enable
RUN npm install -g yarn

# Copy all dependency-related files first
COPY package.json yarn.lock* tsconfig.json ./
COPY prisma ./prisma

# Install all dependencies (including dev dependencies)
RUN yarn install

# Generate prisma client
RUN yarn prisma generate

# Copy root package.json to get zod-to-openapi
COPY ../package.json ../

# Copy remaining source code
COPY . .

# Skip build in development stage - we'll run in dev mode
# RUN yarn build

# Production stage
FROM node:18-alpine AS production
WORKDIR /app

# Install yarn
RUN corepack enable
RUN npm install -g yarn

# Copy package.json and install production dependencies
COPY package.json yarn.lock* ./
COPY --from=development /app/node_modules ./node_modules
RUN yarn install --production

# Copy built files and prisma
COPY --from=development /app/dist ./dist
COPY --from=development /app/prisma ./prisma

EXPOSE 3001
CMD ["yarn", "start"]