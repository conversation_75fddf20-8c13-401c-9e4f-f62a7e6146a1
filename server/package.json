{"name": "server", "version": "1.0.0", "packageManager": "yarn@1.22.22", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev src/index.ts", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "test": "jest --config jest.config.js", "test:watch": "jest --watch --config jest.config.js", "test:coverage": "jest --coverage --config jest.config.js"}, "description": "Backend server for the application", "main": "dist/index.js", "dependencies": {"@prisma/client": "^6.11.1", "@types/jsonwebtoken": "^9.0.10", "argon2": "^0.43.0", "body-parser": "^2.2.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.2", "nodemailer": "^7.0.4", "resend": "^4.6.0", "uuid": "^11.1.0", "winston": "^3.17.0", "yup": "^1.6.1", "zod": "^3.25.74"}, "devDependencies": {"@jest/types": "^30.0.1", "@prisma/engines": "^6.11.1", "@types/express-session": "^1.18.2", "@types/jest": "^30.0.0", "@types/node": "^24.0.10", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "import-local": "^3.2.0", "jest": "^30.0.4", "prisma": "^6.11.1", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}