#!/bin/bash

# Check if yarn is installed
if ! command -v yarn &> /dev/null
then
    echo "yarn could not be found"
    echo "Installing yarn globally..."
    npm install -g yarn
    echo "yarn installed successfully."
fi

echo "Starting development servers for client and client-admin..."

# Start client in dev mode
cd "client"
echo "Starting client development server..."
yarn dev &
CLIENT_PID=$!

# Start client-admin in dev mode
cd "../client-admin"
echo "Starting client-admin development server..."
yarn dev &
CLIENT_ADMIN_PID=$!

# Start server in dev mode
cd "../../server"
echo "Starting server development server..."
yarn dev &
SERVER_PID=$!

echo "All development servers started."

# Trap exit signals to clean up child processes
trap 'kill $CLIENT_PID $CLIENT_ADMIN_PID $SERVER_PID; exit' SIGINT SIGTERM

# Wait indefinitely
wait