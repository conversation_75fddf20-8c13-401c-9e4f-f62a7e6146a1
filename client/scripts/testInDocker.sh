#!/bin/bash

# Check if yarn is installed
if ! command -v yarn &> /dev/null
then
    echo "yarn could not be found"
    echo "Installing yarn globally..."
    npm install -g yarn
    echo "yarn installed successfully."
fi

echo "Testing client in Docker..."

# Function to run commands and handle errors
run_or_exit() {
    echo "Running: $@"
    "$@" || exit $?
}

# Navigate to client directory
cd "client"

# Build the Docker image for testing
run_or_exit docker build -f Dockerfile.test -t client-test .

# Run the tests in the Docker container
run_or_exit docker run --rm client-test

echo "Client tests in Docker completed."