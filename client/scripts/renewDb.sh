#!/bin/bash

# Check if yarn is installed
if ! command -v yarn &> /dev/null
then
    echo "yarn could not be found"
    echo "Installing yarn globally..."
    npm install -g yarn
    echo "yarn installed successfully."
fi

echo "Renewing client database..."

# Function to execute commands
run_or_exit() {
    echo "Running: $@"
    "$@" || exit $?
}

# Remove old migrations and create new ones
cd "client"
run_or_exit npx prisma migrate reset --force

# Seed the database
run_or_exit npx prisma db seed

echo "Client database renewal completed."

fi

echo "🛑 刪除舊的 Prisma 資料庫..."
npx prisma db execute --file <(echo "DROP DATABASE IF EXISTS $(echo $DATABASE_URL | sed -E 's/.*\/([^?]+).*/\1/'); CREATE DATABASE $(echo $DATABASE_URL | sed -E 's/.*\/([^?]+).*/\1/');") --preview-feature

echo "📦 重新生成 Prisma Client..."
npx prisma generate

echo "📤 強制推送 Prisma Schema (跳過遷移)..."
npx prisma db push --force-reset

yarn prisma:seed

echo "✅ Prisma 資料庫已重建完成！"
