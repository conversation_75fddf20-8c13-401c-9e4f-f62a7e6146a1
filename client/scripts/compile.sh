#!/bin/bash

# Check if yarn is installed
if ! command -v yarn &> /dev/null
then
    echo "yarn could not be found"
    echo "Installing yarn globally..."
    npm install -g yarn
    echo "yarn installed successfully."
fi

# Function to compile a directory
compile_dir() {
    local dir="$1"
    echo "Compiling: $dir"
    cd "$dir"
    yarn build
    cd "-"
}

# Compile client
compile_dir "client"

echo "Client compilation completed."
