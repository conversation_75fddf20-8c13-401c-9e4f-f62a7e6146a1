#!/bin/bash

# Check if yarn is installed
if ! command -v yarn &> /dev/null
then
    echo "yarn could not be found"
    echo "Installing yarn globally..."
    npm install -g yarn
    echo "yarn installed successfully."
fi

echo "Compiling client and client-admin..."

# Function to compile a directory
compile_dir() {
    local dir="$1"
    echo "Compiling: $dir"
    cd "$dir"
    yarn build
    cd "-"
}

# Compile client
compile_dir "client"

# Compile client-admin
compile_dir "client-admin"

echo "Compilation completed."

fi

# Build the micro-frontend application
print_app_step "Micro Frontend" "Building the application..."
cd "${MICRO_FRONTEND_DIR}" && yarn build --no-lint || handle_error "Failed to build micro-frontend application"

print_app_step "Micro Frontend" "Compilation completed successfully! 🎉"

#######################################
# Summary
#######################################
print_step "All applications have been successfully compiled! 🚀"
print_step "To run the applications:"
echo -e "  Main App: ${GREEN}cd ${ROOT_DIR} && yarn start${NC}"
echo -e "  Micro Frontend: ${GREEN}cd ${MICRO_FRONTEND_DIR} && yarn start${NC}"
echo ""
print_step "For development mode:"
echo -e "  Main App: ${GREEN}cd ${ROOT_DIR} && yarn dev${NC}"
echo -e "  Micro Frontend: ${GREEN}cd ${MICRO_FRONTEND_DIR} && yarn dev${NC}"

# Make script executable
chmod +x "${ROOT_DIR}/scripts/comboCompile.sh"