#!/bin/bash

# Check if yarn is installed
if ! command -v yarn &> /dev/null
then
    echo "yarn could not be found"
    echo "Installing yarn globally..."
    npm install -g yarn
    echo "yarn installed successfully."
fi

# Function to run commands and handle errors
run_or_exit() {
    echo "Running: $@"
    "$@" || exit $?
}

echo "Testing client database..."

# Navigate to client directory and run database tests
cd "client"
run_or_exit yarn test db

echo "Client database tests completed."
