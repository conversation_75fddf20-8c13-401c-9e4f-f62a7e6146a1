#!/bin/bash

# Check if yarn is installed
if ! command -v yarn &> /dev/null
then
    echo "yarn could not be found"
    echo "Installing yarn globally..."
    npm install -g yarn
    echo "yarn installed successfully."
fi

echo "Starting all servers..."

# Function to start a directory
start_dir() {
    local dir="$1"
    echo "Starting: $dir"
    cd "$dir"
    yarn start &
    cd "-"
    echo "$dir starting..."
}

# Start client
start_dir "client"

# Start client-admin
start_dir "client-admin"

# Start server
start_dir "server"

echo "All servers are starting."