# Admin Micro-Frontend

This is a sample micro-frontend application that demonstrates how to implement Module Federation in Next.js 15+. It exports components that can be consumed by the host application.

## Features

- Built with Next.js 15+
- Uses Webpack Module Federation for exposing components
- Tailwind CSS for styling
- TypeScript for type safety
- Compatible with the main shadcn-template host application

## Exposed Components

This micro-frontend exposes the following components:

- `./ApplicationsManager`: A component for managing system applications

## Local Development

```bash
# Install dependencies
yarn install

# Run the development server
yarn dev
```

The application will be running at http://localhost:3001

## Module Federation Configuration

The configuration is in `next.config.mjs` and uses the `@module-federation/nextjs-mf` plugin to expose components:

```javascript
new NextFederationPlugin({
  name: 'adminApp',
  filename: 'static/chunks/remoteEntry.js',
  exposes: {
    './ApplicationsManager': './components/ApplicationsManager.tsx',
  },
  shared: {
    react: { singleton: true, eager: true, requiredVersion: '^18.0.0' },
    'react-dom': { singleton: true, eager: true, requiredVersion: '^18.0.0' },
  },
})
```

## Integration with Host Application

To consume this micro-frontend in the host application:

1. Make sure the host application is configured to import remote modules
2. Update the host's Module Federation configuration to include this remote
3. Dynamically import the exposed components

Example usage in the host application:

```tsx
// Client component in host app
'use client';
import { loadRemoteComponent } from "@/lib/microfrontend/remoteLoader";

const RemoteApplicationsManager = loadRemoteComponent('applicationsManager');

export function ApplicationsManagerClient({ applications }) {
  return <RemoteApplicationsManager applications={applications} />;
}
```

## Build and Deployment

```bash
# Build for production
yarn build

# Start production server
yarn start
```

For production deployment, ensure this application is deployed to a separate domain or subdomain, and update the host application's environment variables to point to this deployment.