{"name": "sample-application", "version": "0.1.0", "private": true, "packageManager": "yarn@1.22.22", "scripts": {"dev": "next dev -p 3001", "build": "next build --no-lint", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"react": "19.0.0", "react-dom": "19.0.0", "next": "15.1.7", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "tailwind-merge": "3.0.2", "tailwindcss-animate": "1.0.7"}, "devDependencies": {"@module-federation/nextjs-mf": "^7.0.8", "@types/node": "22.0.0", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@typescript-eslint/eslint-plugin": "8.25.0", "@typescript-eslint/parser": "8.25.0", "autoprefixer": "10.4.20", "eslint": "9.21.0", "eslint-config-next": "15.1.7", "eslint-plugin-react": "7.37.4", "node-polyfill-webpack-plugin": "^2.0.1", "postcss": "8.5.3", "tailwindcss": "3.4.1", "typescript": "5.3.3"}}