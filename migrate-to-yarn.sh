#!/bin/bash

# Complete script to migrate from pnpm/npm to yarn
# This script handles the parent directory configuration issue

echo "=== Starting Migration to Yarn ==="

# Step 1: Clean up existing lock files and node_modules
echo "Removing existing lock files and node_modules..."
find . -name "package-lock.json" -type f -delete
find . -name "pnpm-lock.yaml" -type f -delete
find . -name "yarn.lock" -type f -delete
find . -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true

# Step 2: Add .npmrc files with yarn configuration
echo "Creating .npmrc files with yarn configuration..."
cat > ./.npmrc << EOF
engine-strict=false
ignore-workspace-root-check=true
use-yarn=true
legacy-peer-deps=true
EOF

cp ./.npmrc ./client/.npmrc
cp ./.npmrc ./server/.npmrc
cp ./.npmrc ./client-admin/.npmrc
[ -d "./client/sampleApp" ] && cp ./.npmrc ./client/sampleApp/.npmrc

# Step 3: Install yarn globally if not present
if ! command -v yarn &> /dev/null; then
    echo "Installing yarn globally..."
    npm install -g yarn
fi

# Step 4: Install dependencies in each directory
echo "Installing dependencies with yarn..."

# Root directory
echo "Setting up root directory..."
cd /Users/<USER>/Documents/GitHub/shadcn-template
yarn install --ignore-engines || echo "Root installation can be skipped. Continuing..."

# Client directory
echo "Setting up client directory..."
cd /Users/<USER>/Documents/GitHub/shadcn-template/client
yarn install --ignore-engines || echo "Client installation will need to be done manually."

# Client-admin directory
echo "Setting up client-admin directory..."
cd /Users/<USER>/Documents/GitHub/shadcn-template/client-admin
yarn install --ignore-engines || echo "Client-admin installation will need to be done manually."

# Server directory
echo "Setting up server directory..."
cd /Users/<USER>/Documents/GitHub/shadcn-template/server
yarn install --ignore-engines || echo "Server installation will need to be done manually."

# Sample app directory (if exists)
if [ -d "/Users/<USER>/Documents/GitHub/shadcn-template/client/sampleApp" ]; then
    echo "Setting up sampleApp directory..."
    cd /Users/<USER>/Documents/GitHub/shadcn-template/client/sampleApp
    yarn install --ignore-engines || echo "SampleApp installation will need to be done manually."
fi

cd /Users/<USER>/Documents/GitHub/shadcn-template

echo "=== Migration Complete ==="
echo ""
echo "If you encounter issues installing dependencies, try running these commands manually:"
echo ""
echo "cd /Users/<USER>/Documents/GitHub/shadcn-template"
echo "yarn install --ignore-engines"
echo ""
echo "cd /Users/<USER>/Documents/GitHub/shadcn-template/client"
echo "yarn install --ignore-engines"
echo ""
echo "cd /Users/<USER>/Documents/GitHub/shadcn-template/client-admin"
echo "yarn install --ignore-engines"
echo ""
echo "cd /Users/<USER>/Documents/GitHub/shadcn-template/server"
echo "yarn install --ignore-engines"
echo ""
echo "Remember to use 'yarn' instead of 'npm run' or 'pnpm' for all your commands going forward."
