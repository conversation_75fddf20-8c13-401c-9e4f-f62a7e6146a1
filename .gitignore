# ===================================
# Node.js Dependencies
# ===================================
node_modules/
*/node_modules/
**/node_modules/

# Package manager files
package-lock.json
pnpm-lock.yaml
.pnpm-debug.log*

# Yarn (Classic & Berry)
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.yarn/install-state.gz
.pnp.*
.pnp.js
yarn-error.log*
yarn-debug.log*

# ===================================
# Build Outputs & Production
# ===================================
# Next.js
.next/
.next-build/
.next/cache/
.next/standalone/
.next/server/
.next/trace
.next/types
.next-env.d.ts
.next-build-id
out/

# Build directories
build/
dist/
dist-ssr/
.output/

# ===================================
# Environment & Configuration
# ===================================
# Environment files
.env
.env*.local
.env.development
.env.test
.env.production

# ===================================
# Testing & Coverage
# ===================================
# Test outputs
coverage/
.nyc_output/
test-results/
test-logs/
*.lcov

# Jest
jest-coverage/
jest.config.js.bak

# Cypress
cypress/screenshots/
cypress/videos/
cypress/downloads/

# Playwright
playwright-report/
test-results/

# ===================================
# Logs & Debug
# ===================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# ===================================
# Cache & Temporary Files
# ===================================
.cache/
.tmp/
.temp/
*.tmp
*.temp

# Package manager caches
.npm/
.yarn-cache/
.pnpm-store/

# Build tool caches
.eslintcache
.stylelintcache
.prettiercache
.rollup.cache
.parcel-cache/
.nx/

# ===================================
# TypeScript
# ===================================
*.tsbuildinfo
tsconfig.tsbuildinfo
next-env.d.ts

# ===================================
# Database & Prisma
# ===================================
# Prisma
prisma/*.db
prisma/*.db-journal
prisma/dev.db*
prisma/migrations/*_init
server/prisma/*.db
server/prisma/*.db-journal
client-admin/prisma/*.db
client-admin/prisma/*.db-journal

# ===================================
# Deployment & Cloud
# ===================================
# Vercel
.vercel/
.vercel

# ===================================
# PWA & Service Workers
# ===================================
**/public/sw.js
**/public/workbox-*.js
**/public/worker-*.js
**/public/sw.js.map
**/public/workbox-*.js.map
**/public/worker-*.js.map

# ===================================
# IDEs & Editors
# ===================================
# VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws

# Eclipse
.project
.classpath
.settings/
*.launch

# Sublime Text
*.sublime-workspace
*.sublime-project

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc

# ===================================
# OS Generated Files
# ===================================
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
.AppleDouble
.LSOverride
Icon?

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/

# Linux
*~
.fuse_hidden*
.directory
.Trash-*

# ===================================
# Backup & Temporary Files
# ===================================
*.bak
*.backup
*.old
*.orig
*.rej
*.tmp
*.temp

# ===================================
# Security & Certificates
# ===================================
*.pem
*.key
*.crt
*.cert
*.p12
*.pfx

# ===================================
# Project Specific
# ===================================
# Documentation builds
docs/build/
docs/.docusaurus/

# Migration artifacts (already cleaned up)
migrate-to-yarn.sh
convert-to-yarn.sh
init-yarn.sh
use-yarn.sh
*.migration.bak

# Reset files
reset.sql

# Test authentication files
testAuth.md
